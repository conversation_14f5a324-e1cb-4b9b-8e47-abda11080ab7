<meta name='viewport' content='width=device-width, initial-scale=1'/><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Management Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-content {
            padding: 30px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e17055 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .data-management {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .data-management h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .data-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .file-input-label {
            display: inline-block;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .file-input-label:hover {
            transform: translateY(-2px);
        }

        .timeslot-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
        }

        .timeslot-form h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .summary-card .number {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-card .label {
            font-size: 0.9em;
            color: #7f8c8d;
            font-weight: 500;
        }

        .tournament-card {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }

        .tournament-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .tournament-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .tournament-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
        }

        .tournament-type {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .tournament-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
            margin-bottom: 15px;
        }

        .stat {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .stat-number {
            font-size: 1.2em;
            font-weight: 600;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
        }

        .timeslots-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }

        .timeslot-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
        }

        .timeslot-item:last-child {
            margin-bottom: 0;
        }

        .timeslot-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .timeslot-date {
            font-weight: 600;
            color: #2c3e50;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }

        .timeslot-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            font-size: 0.9em;
        }

        .toggle-btn {
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            font-weight: 600;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background 0.2s;
        }

        .toggle-btn:hover {
            background: #e9ecef;
        }

        .hidden {
            display: none;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .storage-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .storage-info h4 {
            margin-bottom: 10px;
            color: #495057;
        }

        .storage-info p {
            margin-bottom: 5px;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .tournament-stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .timeslot-details {
                grid-template-columns: 1fr;
            }

            .data-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 Tournament Manager</h1>
            <p>Track and manage your gaming tournaments locally</p>
        </div>

        <div class="main-content">
            <!-- Data Management Section -->
            <div class="section">
                <h2 class="section-title">💾 Data Management</h2>
                <div class="data-management">
                    <h3>Backup & Restore</h3>
                    <div class="data-actions">
                        <button class="btn btn-success" onclick="downloadData()">📥 Download Data</button>
                        <label for="fileInput" class="file-input-label">📤 Upload Data</label>
                        <input type="file" id="fileInput" class="file-input" accept=".json" onchange="uploadData(event)">
                        <button class="btn btn-danger" onclick="clearAllData()">🗑️ Clear All Data</button>
                    </div>
                    <div class="storage-info">
                        <h4>Storage Information</h4>
                        <p><strong>Data Location:</strong> Your browser's local storage</p>
                        <p><strong>Total Tournaments:</strong> <span id="storageStats">0</span></p>
                        <p><strong>Last Modified:</strong> <span id="lastModified">Never</span></p>
                    </div>
                </div>
            </div>

            <!-- Summary Section -->
            <div class="section">
                <h2 class="section-title">📊 Summary</h2>
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="number" id="totalTournaments">0</div>
                        <div class="label">Total Tournaments</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="totalTimeslots">0</div>
                        <div class="label">Total Timeslots</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="completedTimeslots">0</div>
                        <div class="label">Completed</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="totalPlayers">0</div>
                        <div class="label">Total Players</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="totalCommission">$0</div>
                        <div class="label">Total Commission</div>
                    </div>
                </div>
            </div>

            <!-- Tournament Creation Form -->
            <div class="section">
                <h2 class="section-title">➕ Create Tournament</h2>
                <form id="tournamentForm">
                    <div class="form-group">
                        <label for="tournamentName">Tournament Name:</label>
                        <input type="text" id="tournamentName" required />
                    </div>
                    <div class="form-group">
                        <label for="tournamentType">Tournament Type:</label>
                        <input type="text" id="tournamentType" placeholder="e.g., daily_paid, first_come, practice" required />
                    </div>
                    
                    <div id="timeslotsContainer">
                        <h3>Timeslots</h3>
                        <div id="timeslotsList"></div>
                        <button type="button" class="btn btn-secondary" onclick="addTimeslot()">+ Add Timeslot</button>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn">Save Tournament</button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">Reset Form</button>
                    </div>
                </form>
            </div>

            <!-- Tournaments Dashboard -->
            <div class="section">
                <h2 class="section-title">🎮 Tournaments Dashboard</h2>
                <div id="tournamentsList"></div>
            </div>
        </div>
    </div>

    <script>
        let tournaments = [];
        let timeslotCounter = 0;
        const STORAGE_KEY = 'tournament_data';

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            loadTournaments();
            addTimeslot(); // Add initial timeslot
        });

        // Local Storage Functions
        function saveToStorage() {
            const data = {
                tournaments: tournaments,
                lastModified: new Date().toISOString()
            };
            localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
            updateStorageInfo();
        }

        function loadFromStorage() {
            const data = localStorage.getItem(STORAGE_KEY);
            if (data) {
                try {
                    const parsed = JSON.parse(data);
                    tournaments = parsed.tournaments || [];
                    return parsed;
                } catch (error) {
                    console.error('Error parsing stored data:', error);
                    tournaments = [];
                }
            }
            return { tournaments: [], lastModified: null };
        }

        function updateStorageInfo() {
            const data = loadFromStorage();
            document.getElementById('storageStats').textContent = tournaments.length;
            document.getElementById('lastModified').textContent = data.lastModified ? 
                new Date(data.lastModified).toLocaleString() : 'Never';
        }

        // Data Management Functions
        function downloadData() {
            const data = {
                tournaments: tournaments,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `tournament_data_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showAlert('Data downloaded successfully!', 'success');
        }

        function uploadData(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    
                    // Validate data structure
                    if (!data.tournaments || !Array.isArray(data.tournaments)) {
                        throw new Error('Invalid data format');
                    }
                    
                    // Ask for confirmation
                    if (tournaments.length > 0) {
                        if (!confirm('This will replace all existing data. Are you sure?')) {
                            return;
                        }
                    }
                    
                    // Import data
                    tournaments = data.tournaments;
                    
                    // Generate new IDs if needed
                    tournaments.forEach(tournament => {
                        if (!tournament.id) {
                            tournament.id = generateId();
                        }
                    });
                    
                    saveToStorage();
                    displayTournaments();
                    updateSummary();
                    
                    showAlert(`Successfully imported ${tournaments.length} tournaments!`, 'success');
                } catch (error) {
                    console.error('Error importing data:', error);
                    showAlert('Error importing data. Please check the file format.', 'error');
                }
            };
            reader.readAsText(file);
            
            // Reset file input
            event.target.value = '';
        }

        function clearAllData() {
            if (!confirm('Are you sure you want to delete all tournament data? This cannot be undone.')) {
                return;
            }
            
            tournaments = [];
            localStorage.removeItem(STORAGE_KEY);
            displayTournaments();
            updateSummary();
            updateStorageInfo();
            showAlert('All data cleared successfully!', 'success');
        }

        // Utility Functions
        function generateId() {
            return Date.now().toString(36) + Math.random().toString(36).substr(2);
        }

        // Add timeslot form
        function addTimeslot() {
            timeslotCounter++;
            const timeslotHtml = `
                <div class="timeslot-form" id="timeslot-${timeslotCounter}">
                    <h4>Timeslot ${timeslotCounter}</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Date & Time:</label>
                            <input type="datetime-local" name="date" required />
                        </div>
                        <div class="form-group">
                            <label>Players Joined:</label>
                            <input type="number" name="playersJoined" min="0" required />
                        </div>
                        <div class="form-group">
                            <label>Commission:</label>
                            <input type="number" name="commission" min="0" step="0.01" required />
                        </div>
                        <div class="form-group">
                            <label>Status:</label>
                            <select name="status" required>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Notes (optional):</label>
                        <textarea name="notes" rows="2" placeholder="Any additional notes..."></textarea>
                    </div>
                    <button type="button" class="btn btn-danger btn-small" onclick="removeTimeslot(${timeslotCounter})">Remove Timeslot</button>
                </div>
            `;
            document.getElementById('timeslotsList').insertAdjacentHTML('beforeend', timeslotHtml);
        }

        // Remove timeslot
        function removeTimeslot(id) {
            const timeslot = document.getElementById(`timeslot-${id}`);
            if (timeslot) {
                timeslot.remove();
            }
        }

        // Handle form submission
        document.getElementById('tournamentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const tournamentData = {
                id: generateId(),
                name: document.getElementById('tournamentName').value,
                type: document.getElementById('tournamentType').value,
                timeslots: [],
                createdAt: new Date().toISOString()
            };

            // Collect timeslot data
            const timeslotForms = document.querySelectorAll('.timeslot-form');
            timeslotForms.forEach(form => {
                const timeslot = {
                    id: generateId(),
                    date: form.querySelector('input[name="date"]').value,
                    playersJoined: parseInt(form.querySelector('input[name="playersJoined"]').value),
                    commission: parseFloat(form.querySelector('input[name="commission"]').value),
                    status: form.querySelector('select[name="status"]').value,
                    notes: form.querySelector('textarea[name="notes"]').value
                };
                tournamentData.timeslots.push(timeslot);
            });

            tournaments.push(tournamentData);
            saveToStorage();
            showAlert('Tournament saved successfully!', 'success');
            resetForm();
            displayTournaments();
            updateSummary();
        });

        // Load tournaments
        function loadTournaments() {
            loadFromStorage();
            displayTournaments();
            updateSummary();
            updateStorageInfo();
        }

        // Display tournaments
        function displayTournaments() {
            const container = document.getElementById('tournamentsList');
            if (tournaments.length === 0) {
                container.innerHTML = '<p>No tournaments found. Create your first tournament above!</p>';
                return;
            }

            container.innerHTML = tournaments.map(tournament => `
                <div class="tournament-card">
                    <div class="tournament-header">
                        <div class="tournament-title">${tournament.name}</div>
                        <div class="tournament-type">${tournament.type}</div>
                    </div>
                    <div class="tournament-stats">
                        <div class="stat">
                            <div class="stat-number">${tournament.timeslots.length}</div>
                            <div class="stat-label">Timeslots</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">${tournament.timeslots.filter(t => t.status === 'completed').length}</div>
                            <div class="stat-label">Completed</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">${tournament.timeslots.filter(t => t.status === 'cancelled').length}</div>
                            <div class="stat-label">Cancelled</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">${tournament.timeslots.reduce((sum, t) => sum + t.playersJoined, 0)}</div>
                            <div class="stat-label">Players</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">$${tournament.timeslots.reduce((sum, t) => sum + t.commission, 0).toFixed(2)}</div>
                            <div class="stat-label">Commission</div>
                        </div>
                    </div>
                    <button class="toggle-btn" onclick="toggleTimeslots('${tournament.id}')">
                        <span id="toggle-text-${tournament.id}">Show Timeslots</span>
                    </button>
                    <div class="timeslots-container hidden" id="timeslots-${tournament.id}">
                        ${tournament.timeslots.map(timeslot => `
                            <div class="timeslot-item">
                                <div class="timeslot-header">
                                    <div class="timeslot-date">${new Date(timeslot.date).toLocaleString()}</div>
                                    <div class="status-badge status-${timeslot.status}">${timeslot.status}</div>
                                </div>
                                <div class="timeslot-details">
                                    <div><strong>Players:</strong> ${timeslot.playersJoined}</div>
                                    <div><strong>Commission:</strong> $${timeslot.commission.toFixed(2)}</div>
                                    ${timeslot.notes ? `<div><strong>Notes:</strong> ${timeslot.notes}</div>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <div class="actions">
                        <button class="btn btn-danger btn-small" onclick="deleteTournament('${tournament.id}')">Delete</button>
                    </div>
                </div>
            `).join('');
        }

        // Toggle timeslots visibility
        function toggleTimeslots(tournamentId) {
            const container = document.getElementById(`timeslots-${tournamentId}`);
            const toggleText = document.getElementById(`toggle-text-${tournamentId}`);
            
            if (container.classList.contains('hidden')) {
                container.classList.remove('hidden');
                toggleText.textContent = 'Hide Timeslots';
            } else {
                container.classList.add('hidden');
                toggleText.textContent = 'Show Timeslots';
            }
        }

        // Delete tournament
        function deleteTournament(tournamentId) {
            if (!confirm('Are you sure you want to delete this tournament?')) return;

            tournaments = tournaments.filter(t => t.id !== tournamentId);
            saveToStorage();
            showAlert('Tournament deleted successfully!', 'success');
            displayTournaments();
            updateSummary();
        }

        // Update summary statistics
        function updateSummary() {
            const totalTournaments = tournaments.length;
            let totalTimeslots = 0;
            let completedTimeslots = 0;
            let totalPlayers = 0;
            let totalCommission = 0;

            tournaments.forEach(tournament => {
                totalTimeslots += tournament.timeslots.length;
                tournament.timeslots.forEach(timeslot => {
                    if (timeslot.status === 'completed') {
                        completedTimeslots++;
                    }
                    totalPlayers += timeslot.playersJoined;
                    totalCommission += timeslot.commission;
                });
            });

            document.getElementById('totalTournaments').textContent = totalTournaments;
            document.getElementById('totalTimeslots').textContent = totalTimeslots;
            document.getElementById('completedTimeslots').textContent = completedTimeslots;
            document.getElementById('totalPlayers').textContent = totalPlayers;
            document.getElementById('totalCommission').textContent = `$${totalCommission.toFixed(2)}`;
        }

        // Reset form
        function resetForm() {
            document.getElementById('tournamentForm').reset();
            document.getElementById('timeslotsList').innerHTML = '';
            timeslotCounter = 0;
            addTimeslot();
        }

        // Show alert
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '1000';
            alertDiv.style.maxWidth = '400px';
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
from flask import Flask, request, jsonify, render_template_string, session, redirect, url_for
import os
from datetime import datetime, timedelta, timezone
import secrets
from functools import wraps
import json
import logging
from pymongo import MongoClient, ASCENDING, DESCENDING
from bson import ObjectId

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'your-secret-key-change-this')

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# MongoDB connection
MONGODB_URI = "mongodb+srv://truckkun:<EMAIL>/?retryWrites=true&w=majority"

try:
    client = MongoClient(MONGODB_URI, serverSelectionTimeoutMS=5000)
    # Test connection
    client.admin.command('ping')
    logger.info("Successfully connected to MongoDB")

    db = client.tournament_db
    tournaments_collection = db.tournaments
    free_games_collection = db.free_games

    # Create indexes for better performance
    tournaments_collection.create_index([("createdAt", DESCENDING)])
    tournaments_collection.create_index([("name", ASCENDING)])
    tournaments_collection.create_index([("type", ASCENDING)])

    free_games_collection.create_index([("date", DESCENDING)])
    free_games_collection.create_index([("createdAt", DESCENDING)])

except Exception as e:
    logger.error(f"Failed to connect to MongoDB: {e}")
    # In production, you might want to handle this differently
    client = None
    db = None
    tournaments_collection = None
    free_games_collection = None

# Admin password (in production, use environment variable)
ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'authenticated' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def serialize_doc(doc):
    """Convert MongoDB document to JSON serializable format"""
    if doc is None:
        return None
    if isinstance(doc, list):
        return [serialize_doc(item) for item in doc]
    if isinstance(doc, dict):
        result = {}
        for key, value in doc.items():
            if key == '_id':
                result['id'] = str(value)
            elif isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.isoformat()
            elif isinstance(value, dict):
                result[key] = serialize_doc(value)
            elif isinstance(value, list):
                result[key] = serialize_doc(value)
            else:
                result[key] = value
        return result
    return doc

def validate_tournament_data(data):
    """Validate tournament data"""
    errors = []

    if not data.get('name') or not isinstance(data['name'], str) or len(data['name'].strip()) == 0:
        errors.append('Tournament name is required and must be a non-empty string')

    if not data.get('type') or not isinstance(data['type'], str) or len(data['type'].strip()) == 0:
        errors.append('Tournament type is required and must be a non-empty string')

    if 'timeslots' in data and not isinstance(data['timeslots'], list):
        errors.append('Timeslots must be a list')

    # Validate timeslots if provided
    if 'timeslots' in data and isinstance(data['timeslots'], list):
        for i, timeslot in enumerate(data['timeslots']):
            if not isinstance(timeslot, dict):
                errors.append(f'Timeslot {i+1} must be an object')
                continue

            if 'date' not in timeslot:
                errors.append(f'Timeslot {i+1} must have a date')

            if 'playersJoined' not in timeslot or not isinstance(timeslot['playersJoined'], (int, float)) or timeslot['playersJoined'] < 0:
                errors.append(f'Timeslot {i+1} must have a valid playersJoined number (>= 0)')

            if 'commission' not in timeslot or not isinstance(timeslot['commission'], (int, float)) or timeslot['commission'] < 0:
                errors.append(f'Timeslot {i+1} must have a valid commission amount (>= 0)')

            if 'status' not in timeslot or timeslot['status'] not in ['completed', 'cancelled']:
                errors.append(f'Timeslot {i+1} must have a valid status (completed or cancelled)')

    return errors

def validate_free_game_data(data):
    """Validate free game data"""
    errors = []

    if not data.get('date'):
        errors.append('Date is required')
    else:
        try:
            datetime.fromisoformat(data['date'].replace('Z', '+00:00'))
        except (ValueError, AttributeError):
            errors.append('Date must be a valid ISO format date')

    if 'amount' not in data:
        errors.append('Amount is required')
    else:
        try:
            amount = float(data['amount'])
            if amount < 0:
                errors.append('Amount must be a positive number')
        except (ValueError, TypeError):
            errors.append('Amount must be a valid number')

    if 'description' in data and not isinstance(data['description'], str):
        errors.append('Description must be a string')

    return errors

@app.route('/')
def index():
    if 'authenticated' not in session:
        return redirect(url_for('login'))
    return render_template_string(get_admin_template())

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        password = request.form.get('password')
        if password == ADMIN_PASSWORD:
            session['authenticated'] = True
            return redirect(url_for('index'))
        else:
            return render_template_string(get_login_template(), error="Invalid password")
    return render_template_string(get_login_template())

@app.route('/logout')
def logout():
    session.pop('authenticated', None)
    return redirect(url_for('login'))

@app.route('/static/<path:filename>')
def serve_static(filename):
    try:
        if filename == 'app.js':
            with open('static/app.js', 'r', encoding='utf-8') as f:
                content = f.read()
            response = app.response_class(
                content,
                mimetype='application/javascript'
            )
            return response
        else:
            return "File not found", 404
    except FileNotFoundError:
        return "File not found", 404

# API Routes
@app.route('/api/tournaments', methods=['GET'])
@login_required
def get_tournaments():
    try:
        if tournaments_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        # Get query parameters for filtering and sorting
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        sort_by = request.args.get('sort_by', 'createdAt')  # createdAt, commission
        sort_order = request.args.get('sort_order', 'desc')  # asc, desc

        # Build query
        query = {}

        # Apply date filtering if provided
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    date_filter['$gte'] = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({'error': 'Invalid start_date format'}), 400
            if end_date:
                try:
                    date_filter['$lte'] = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({'error': 'Invalid end_date format'}), 400
            query['createdAt'] = date_filter

        # Build sort criteria
        sort_direction = DESCENDING if sort_order == 'desc' else ASCENDING

        if sort_by == 'commission':
            # For commission sorting, we need to use aggregation pipeline
            pipeline = [
                {'$match': query},
                {
                    '$addFields': {
                        'totalCommission': {
                            '$sum': '$timeslots.commission'
                        }
                    }
                },
                {'$sort': {'totalCommission': sort_direction}}
            ]
            tournaments = list(tournaments_collection.aggregate(pipeline))
        else:
            # Simple sorting by field
            tournaments = list(tournaments_collection.find(query).sort(sort_by, sort_direction))

        return jsonify(serialize_doc(tournaments))
    except Exception as e:
        logger.error(f"Error fetching tournaments: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tournaments', methods=['POST'])
@login_required
def create_tournament():
    try:
        if tournaments_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_errors = validate_tournament_data(data)
        if validation_errors:
            return jsonify({'error': 'Validation failed', 'details': validation_errors}), 400

        # Process timeslots to ensure proper data types
        timeslots = []
        for timeslot in data.get('timeslots', []):
            processed_timeslot = {
                'id': str(ObjectId()),  # Generate unique ID for timeslot
                'date': timeslot['date'],
                'playersJoined': int(timeslot['playersJoined']),
                'commission': float(timeslot['commission']),
                'status': timeslot['status'],
                'notes': timeslot.get('notes', '')
            }
            timeslots.append(processed_timeslot)

        tournament = {
            'name': data['name'].strip(),
            'type': data['type'].strip(),
            'timeslots': timeslots,
            'createdAt': datetime.now(timezone.utc)
        }

        result = tournaments_collection.insert_one(tournament)
        tournament['id'] = str(result.inserted_id)

        logger.info(f"Created tournament: {tournament['name']} with ID: {tournament['id']}")
        return jsonify(serialize_doc(tournament)), 201
    except Exception as e:
        logger.error(f"Error creating tournament: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tournaments/<tournament_id>', methods=['PUT'])
@login_required
def update_tournament(tournament_id):
    try:
        if tournaments_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate ObjectId
        try:
            ObjectId(tournament_id)
        except:
            return jsonify({'error': 'Invalid tournament ID'}), 400

        # Validate data if provided
        if 'name' in data or 'type' in data or 'timeslots' in data:
            validation_errors = validate_tournament_data(data)
            if validation_errors:
                return jsonify({'error': 'Validation failed', 'details': validation_errors}), 400

        update_data = {}
        if 'name' in data:
            update_data['name'] = data['name'].strip()
        if 'type' in data:
            update_data['type'] = data['type'].strip()
        if 'timeslots' in data:
            # Process timeslots to ensure proper data types
            timeslots = []
            for timeslot in data['timeslots']:
                processed_timeslot = {
                    'id': timeslot.get('id', str(ObjectId())),
                    'date': timeslot['date'],
                    'playersJoined': int(timeslot['playersJoined']),
                    'commission': float(timeslot['commission']),
                    'status': timeslot['status'],
                    'notes': timeslot.get('notes', '')
                }
                timeslots.append(processed_timeslot)
            update_data['timeslots'] = timeslots

        result = tournaments_collection.update_one(
            {'_id': ObjectId(tournament_id)},
            {'$set': update_data}
        )

        if result.matched_count == 0:
            return jsonify({'error': 'Tournament not found'}), 404

        updated_tournament = tournaments_collection.find_one({'_id': ObjectId(tournament_id)})
        logger.info(f"Updated tournament: {tournament_id}")
        return jsonify(serialize_doc(updated_tournament))
    except Exception as e:
        logger.error(f"Error updating tournament: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/tournaments/<tournament_id>', methods=['DELETE'])
@login_required
def delete_tournament(tournament_id):
    try:
        if tournaments_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        # Validate ObjectId
        try:
            ObjectId(tournament_id)
        except:
            return jsonify({'error': 'Invalid tournament ID'}), 400

        result = tournaments_collection.delete_one({'_id': ObjectId(tournament_id)})

        if result.deleted_count == 0:
            return jsonify({'error': 'Tournament not found'}), 404

        logger.info(f"Deleted tournament: {tournament_id}")
        return jsonify({'message': 'Tournament deleted successfully'})
    except Exception as e:
        logger.error(f"Error deleting tournament: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/free-games', methods=['GET'])
@login_required
def get_free_games():
    try:
        if free_games_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        # Get query parameters for filtering and sorting
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        sort_by = request.args.get('sort_by', 'date')  # date, amount
        sort_order = request.args.get('sort_order', 'desc')  # asc, desc

        # Build query
        query = {}

        # Apply date filtering if provided
        if start_date or end_date:
            date_filter = {}
            if start_date:
                try:
                    date_filter['$gte'] = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({'error': 'Invalid start_date format'}), 400
            if end_date:
                try:
                    date_filter['$lte'] = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                except ValueError:
                    return jsonify({'error': 'Invalid end_date format'}), 400
            query['date'] = date_filter

        # Build sort criteria
        sort_direction = DESCENDING if sort_order == 'desc' else ASCENDING

        free_games = list(free_games_collection.find(query).sort(sort_by, sort_direction))
        return jsonify(serialize_doc(free_games))
    except Exception as e:
        logger.error(f"Error fetching free games: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/free-games', methods=['POST'])
@login_required
def create_free_game():
    try:
        if free_games_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate data
        validation_errors = validate_free_game_data(data)
        if validation_errors:
            return jsonify({'error': 'Validation failed', 'details': validation_errors}), 400

        free_game = {
            'date': datetime.fromisoformat(data['date'].replace('Z', '+00:00')),
            'amount': float(data['amount']),
            'description': data.get('description', '').strip(),
            'createdAt': datetime.now(timezone.utc)
        }

        result = free_games_collection.insert_one(free_game)
        free_game['id'] = str(result.inserted_id)

        logger.info(f"Created free game record with ID: {free_game['id']}")
        return jsonify(serialize_doc(free_game)), 201
    except Exception as e:
        logger.error(f"Error creating free game: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/free-games/<game_id>', methods=['PUT'])
@login_required
def update_free_game(game_id):
    try:
        if free_games_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate ObjectId
        try:
            ObjectId(game_id)
        except:
            return jsonify({'error': 'Invalid free game ID'}), 400

        # Validate data if provided
        validation_errors = validate_free_game_data(data)
        if validation_errors:
            return jsonify({'error': 'Validation failed', 'details': validation_errors}), 400

        update_data = {}
        if 'date' in data:
            update_data['date'] = datetime.fromisoformat(data['date'].replace('Z', '+00:00'))
        if 'amount' in data:
            update_data['amount'] = float(data['amount'])
        if 'description' in data:
            update_data['description'] = data['description'].strip()

        result = free_games_collection.update_one(
            {'_id': ObjectId(game_id)},
            {'$set': update_data}
        )

        if result.matched_count == 0:
            return jsonify({'error': 'Free game record not found'}), 404

        updated_game = free_games_collection.find_one({'_id': ObjectId(game_id)})
        logger.info(f"Updated free game: {game_id}")
        return jsonify(serialize_doc(updated_game))
    except Exception as e:
        logger.error(f"Error updating free game: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/free-games/<game_id>', methods=['DELETE'])
@login_required
def delete_free_game(game_id):
    try:
        if free_games_collection is None:
            return jsonify({'error': 'Database connection not available'}), 500

        # Validate ObjectId
        try:
            ObjectId(game_id)
        except:
            return jsonify({'error': 'Invalid free game ID'}), 400

        result = free_games_collection.delete_one({'_id': ObjectId(game_id)})

        if result.deleted_count == 0:
            return jsonify({'error': 'Free game record not found'}), 404

        logger.info(f"Deleted free game: {game_id}")
        return jsonify({'message': 'Free game record deleted successfully'})
    except Exception as e:
        logger.error(f"Error deleting free game: {e}")
        return jsonify({'error': str(e)}), 500

def get_login_template():
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Manager - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header h1 {
            color: #2c3e50;
            font-size: 2em;
            margin-bottom: 10px;
        }
        .login-header p {
            color: #7f8c8d;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>🏆 Tournament Manager</h1>
            <p>Please enter your password to continue</p>
        </div>
        {% if error %}
        <div class="error">{{ error }}</div>
        {% endif %}
        <form method="POST">
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">Login</button>
        </form>
    </div>
</body>
</html>
    '''

def get_admin_template():
    # Use the new enhanced template
    try:
        with open('templates/admin.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return get_fallback_template()

def get_enhanced_styles():
    return '''
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s;
        }

        .tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .filters h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e17055 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .summary-card .number {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-card .label {
            font-size: 0.9em;
            color: #7f8c8d;
            font-weight: 500;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }

            .filter-row {
                grid-template-columns: 1fr;
            }
        }
    '''

def get_enhanced_javascript():
    return '''
        // Global variables
        let currentTab = 'tournaments';
        let tournaments = [];
        let freeGames = [];

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            initializeTabs();
            loadTournaments();
            loadFreeGames();
        });

        // Tab functionality
        function initializeTabs() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.dataset.tab;
                    switchTab(tabId);
                });
            });
        }

        function switchTab(tabId) {
            // Update tab buttons
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

            // Update tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabId}-tab`).classList.add('active');

            currentTab = tabId;
        }

        // API functions
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.error || 'Request failed');
                }

                return await response.json();
            } catch (error) {
                console.error('API request failed:', error);
                showAlert(error.message, 'error');
                throw error;
            }
        }

        async function loadTournaments() {
            try {
                tournaments = await apiRequest('/api/tournaments');
                displayTournaments();
                updateTournamentSummary();
            } catch (error) {
                console.error('Failed to load tournaments:', error);
            }
        }

        async function loadFreeGames() {
            try {
                freeGames = await apiRequest('/api/free-games');
                displayFreeGames();
                updateFreeGamesSummary();
            } catch (error) {
                console.error('Failed to load free games:', error);
            }
        }

        function displayTournaments() {
            // Implementation will be added in the next step
            console.log('Displaying tournaments:', tournaments);
        }

        function displayFreeGames() {
            // Implementation will be added in the next step
            console.log('Displaying free games:', freeGames);
        }

        function updateTournamentSummary() {
            // Implementation will be added in the next step
            console.log('Updating tournament summary');
        }

        function updateFreeGamesSummary() {
            // Implementation will be added in the next step
            console.log('Updating free games summary');
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            alertDiv.style.position = 'fixed';
            alertDiv.style.top = '20px';
            alertDiv.style.right = '20px';
            alertDiv.style.zIndex = '1000';
            alertDiv.style.maxWidth = '400px';

            document.body.appendChild(alertDiv);

            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    '''

def get_fallback_template():
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Management Tool</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .error { color: red; background: #ffe6e6; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="error">
        <h2>Template Loading Error</h2>
        <p>Could not load the admin template. Please check the file structure.</p>
        <p><a href="/logout">Logout</a></p>
    </div>
</body>
</html>
    '''

# Vercel serverless function handler
def handler(request):
    return app(request.environ, lambda status, headers: None)

if __name__ == '__main__':
    app.run(debug=True)

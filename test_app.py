#!/usr/bin/env python3
"""
Simple test script to validate the tournament management application
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"  # Change this for production testing
ADMIN_PASSWORD = "admin123"  # Change this to match your admin password

def test_login():
    """Test the login functionality"""
    print("Testing login...")
    
    # Test login page
    response = requests.get(f"{BASE_URL}/login")
    if response.status_code == 200:
        print("✓ Login page accessible")
    else:
        print("✗ Login page not accessible")
        return False
    
    # Test login with correct password
    session = requests.Session()
    response = session.post(f"{BASE_URL}/login", data={"password": ADMIN_PASSWORD})
    if response.status_code == 200 and "Tournament Manager" in response.text:
        print("✓ Login successful")
        return session
    else:
        print("✗ Login failed")
        return False

def test_tournaments_api(session):
    """Test tournament API endpoints"""
    print("\nTesting tournaments API...")
    
    # Test GET tournaments
    response = session.get(f"{BASE_URL}/api/tournaments")
    if response.status_code == 200:
        print("✓ GET tournaments successful")
        tournaments = response.json()
        print(f"  Found {len(tournaments)} tournaments")
    else:
        print("✗ GET tournaments failed")
        return False
    
    # Test POST tournament
    test_tournament = {
        "name": "Test Tournament",
        "type": "test",
        "timeslots": [
            {
                "date": "2024-01-01T10:00:00",
                "playersJoined": 5,
                "commission": 25.50,
                "status": "completed",
                "notes": "Test timeslot"
            }
        ]
    }
    
    response = session.post(
        f"{BASE_URL}/api/tournaments",
        json=test_tournament,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 201:
        print("✓ POST tournament successful")
        created_tournament = response.json()
        tournament_id = created_tournament["id"]
        
        # Test DELETE tournament
        response = session.delete(f"{BASE_URL}/api/tournaments/{tournament_id}")
        if response.status_code == 200:
            print("✓ DELETE tournament successful")
        else:
            print("✗ DELETE tournament failed")
    else:
        print("✗ POST tournament failed")
        print(f"  Response: {response.text}")
    
    return True

def test_free_games_api(session):
    """Test free games API endpoints"""
    print("\nTesting free games API...")
    
    # Test GET free games
    response = session.get(f"{BASE_URL}/api/free-games")
    if response.status_code == 200:
        print("✓ GET free games successful")
        free_games = response.json()
        print(f"  Found {len(free_games)} free game records")
    else:
        print("✗ GET free games failed")
        return False
    
    # Test POST free game
    test_free_game = {
        "date": "2024-01-01T00:00:00Z",
        "amount": 50.00,
        "description": "Test free game event"
    }
    
    response = session.post(
        f"{BASE_URL}/api/free-games",
        json=test_free_game,
        headers={"Content-Type": "application/json"}
    )
    
    if response.status_code == 201:
        print("✓ POST free game successful")
        created_game = response.json()
        game_id = created_game["id"]
        
        # Test DELETE free game
        response = session.delete(f"{BASE_URL}/api/free-games/{game_id}")
        if response.status_code == 200:
            print("✓ DELETE free game successful")
        else:
            print("✗ DELETE free game failed")
    else:
        print("✗ POST free game failed")
        print(f"  Response: {response.text}")
    
    return True

def test_static_files(session):
    """Test static file serving"""
    print("\nTesting static files...")
    
    response = session.get(f"{BASE_URL}/static/app.js")
    if response.status_code == 200 and "application/javascript" in response.headers.get("Content-Type", ""):
        print("✓ Static JavaScript file served correctly")
    else:
        print("✗ Static JavaScript file not served correctly")

def main():
    """Run all tests"""
    print("Starting Tournament Management App Tests")
    print("=" * 50)
    
    try:
        # Test login
        session = test_login()
        if not session:
            print("\nTests failed at login step")
            return
        
        # Test APIs
        test_tournaments_api(session)
        test_free_games_api(session)
        test_static_files(session)
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("✗ Could not connect to the application")
        print("  Make sure the application is running on", BASE_URL)
    except Exception as e:
        print(f"✗ Test failed with error: {e}")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Management Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }

        .logout-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e9ecef;
        }

        .tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s;
        }

        .tab.active {
            background: white;
            color: #667eea;
            border-bottom: 3px solid #667eea;
        }

        .tab:hover:not(.active) {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .filters {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .filters h3 {
            margin-bottom: 15px;
            color: #495057;
        }

        .filter-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fd79a8 0%, #e17055 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .summary-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .summary-card {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .summary-card .number {
            font-size: 2.5em;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .summary-card .label {
            font-size: 0.9em;
            color: #7f8c8d;
            font-weight: 500;
        }

        .section {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 1.8em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .hidden {
            display: none;
        }

        .tournament-card {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }

        .tournament-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .free-game-card {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: transform 0.2s;
        }

        .free-game-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .timeslot-form {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
        }

        .timeslot-form h4 {
            margin-bottom: 15px;
            color: #495057;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        @media (max-width: 768px) {
            .tabs {
                flex-direction: column;
            }
            
            .filter-row {
                grid-template-columns: 1fr;
            }
            
            .summary-cards {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/logout" class="logout-btn">Logout</a>
            <h1>🏆 Tournament Manager</h1>
            <p>Manage your gaming tournaments and free games</p>
        </div>

        <div class="tabs">
            <button class="tab active" data-tab="tournaments">🎮 Tournaments</button>
            <button class="tab" data-tab="free-games">🎁 FREE Games</button>
        </div>

        <!-- Tournaments Tab -->
        <div id="tournaments-tab" class="tab-content active">
            <!-- Filters -->
            <div class="filters">
                <h3>🔍 Filter & Sort Tournaments</h3>
                <div class="filter-row">
                    <div class="form-group">
                        <label for="tournament-start-date">Start Date:</label>
                        <input type="date" id="tournament-start-date">
                    </div>
                    <div class="form-group">
                        <label for="tournament-end-date">End Date:</label>
                        <input type="date" id="tournament-end-date">
                    </div>
                    <div class="form-group">
                        <label for="tournament-sort">Sort By:</label>
                        <select id="tournament-sort">
                            <option value="createdAt">Date Created</option>
                            <option value="commission">Total Commission</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tournament-order">Order:</label>
                        <select id="tournament-order">
                            <option value="desc">Descending</option>
                            <option value="asc">Ascending</option>
                        </select>
                    </div>
                </div>
                <button class="btn" onclick="applyTournamentFilters()">Apply Filters</button>
                <button class="btn btn-secondary" onclick="clearTournamentFilters()">Clear</button>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2 class="section-title">📊 Tournament Summary</h2>
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="number" id="total-tournaments">0</div>
                        <div class="label">Total Tournaments</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="total-commission">$0</div>
                        <div class="label">Total Commission</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="filtered-commission">$0</div>
                        <div class="label">Filtered Commission</div>
                    </div>
                </div>
            </div>

            <!-- Add New Tournament -->
            <div class="section">
                <h2 class="section-title">➕ Create Tournament</h2>
                <form id="tournament-form">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="tournament-name">Tournament Name:</label>
                            <input type="text" id="tournament-name" required>
                        </div>
                        <div class="form-group">
                            <label for="tournament-type">Tournament Type:</label>
                            <input type="text" id="tournament-type" placeholder="e.g., daily_paid, first_come, practice" required>
                        </div>
                    </div>

                    <div id="timeslots-container">
                        <h3>Timeslots</h3>
                        <div id="timeslots-list"></div>
                        <button type="button" class="btn btn-secondary" onclick="addTimeslot()">+ Add Timeslot</button>
                    </div>

                    <div style="margin-top: 20px;">
                        <button type="submit" class="btn">Save Tournament</button>
                        <button type="button" class="btn btn-secondary" onclick="resetTournamentForm()">Reset Form</button>
                    </div>
                </form>
            </div>

            <!-- Tournament List -->
            <div class="section">
                <h2 class="section-title">🎮 Tournaments</h2>
                <div id="tournaments-list" class="loading">Loading tournaments...</div>
            </div>
        </div>

        <!-- FREE Games Tab -->
        <div id="free-games-tab" class="tab-content">
            <!-- Filters -->
            <div class="filters">
                <h3>🔍 Filter & Sort FREE Games</h3>
                <div class="filter-row">
                    <div class="form-group">
                        <label for="freegame-start-date">Start Date:</label>
                        <input type="date" id="freegame-start-date">
                    </div>
                    <div class="form-group">
                        <label for="freegame-end-date">End Date:</label>
                        <input type="date" id="freegame-end-date">
                    </div>
                    <div class="form-group">
                        <label for="freegame-sort">Sort By:</label>
                        <select id="freegame-sort">
                            <option value="date">Date</option>
                            <option value="amount">Amount</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="freegame-order">Order:</label>
                        <select id="freegame-order">
                            <option value="desc">Descending</option>
                            <option value="asc">Ascending</option>
                        </select>
                    </div>
                </div>
                <button class="btn" onclick="applyFreeGameFilters()">Apply Filters</button>
                <button class="btn btn-secondary" onclick="clearFreeGameFilters()">Clear</button>
            </div>

            <!-- Add New FREE Game -->
            <div class="section">
                <h2 class="section-title">➕ Add FREE Game Expenditure</h2>
                <form id="free-game-form">
                    <div class="filter-row">
                        <div class="form-group">
                            <label for="freegame-date">Date:</label>
                            <input type="date" id="freegame-date" required>
                        </div>
                        <div class="form-group">
                            <label for="freegame-amount">Amount Spent:</label>
                            <input type="number" id="freegame-amount" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="freegame-description">Description (optional):</label>
                        <textarea id="freegame-description" rows="3" placeholder="Notes about the free game event..."></textarea>
                    </div>
                    <button type="submit" class="btn">Save FREE Game Record</button>
                </form>
            </div>

            <!-- Summary -->
            <div class="section">
                <h2 class="section-title">📊 FREE Games Summary</h2>
                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="number" id="total-free-games">0</div>
                        <div class="label">Total Records</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="total-expenditure">$0</div>
                        <div class="label">Total Expenditure</div>
                    </div>
                    <div class="summary-card">
                        <div class="number" id="filtered-expenditure">$0</div>
                        <div class="label">Filtered Expenditure</div>
                    </div>
                </div>
            </div>

            <!-- FREE Games List -->
            <div class="section">
                <h2 class="section-title">🎁 FREE Games Records</h2>
                <div id="free-games-list" class="loading">Loading free games...</div>
            </div>
        </div>
    </div>

    <script src="/static/app.js"></script>
</body>
</html>

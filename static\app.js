// Global variables
let currentTab = 'tournaments';
let tournaments = [];
let freeGames = [];
let filteredTournaments = [];
let filteredFreeGames = [];
let timeslotCounter = 0;

// Initialize the app
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeForms();
    loadTournaments();
    loadFreeGames();
});

// Tab functionality
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const tabId = tab.dataset.tab;
            switchTab(tabId);
        });
    });
}

function switchTab(tabId) {
    // Update tab buttons
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabId}-tab`).classList.add('active');
    
    currentTab = tabId;
}

// Form initialization
function initializeForms() {
    // Tournament form
    const tournamentForm = document.getElementById('tournament-form');
    if (tournamentForm) {
        tournamentForm.addEventListener('submit', handleTournamentSubmit);
    }

    // FREE Games form
    const freeGameForm = document.getElementById('free-game-form');
    if (freeGameForm) {
        freeGameForm.addEventListener('submit', handleFreeGameSubmit);
    }

    // Set default date to today
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInputs.forEach(input => {
        if (input.id === 'freegame-date') {
            input.value = today;
        }
    });

    // Add initial timeslot
    addTimeslot();
}

// API functions
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Request failed');
        }
        
        return await response.json();
    } catch (error) {
        console.error('API request failed:', error);
        showAlert(error.message, 'error');
        throw error;
    }
}

// Tournament functions
async function loadTournaments() {
    try {
        tournaments = await apiRequest('/api/tournaments');
        filteredTournaments = [...tournaments];
        displayTournaments();
        updateTournamentSummary();
    } catch (error) {
        console.error('Failed to load tournaments:', error);
        document.getElementById('tournaments-list').innerHTML = 
            '<div class="alert alert-error">Failed to load tournaments. Please try again.</div>';
    }
}

async function applyTournamentFilters() {
    try {
        const startDate = document.getElementById('tournament-start-date').value;
        const endDate = document.getElementById('tournament-end-date').value;
        const sortBy = document.getElementById('tournament-sort').value;
        const sortOrder = document.getElementById('tournament-order').value;
        
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate + 'T00:00:00Z');
        if (endDate) params.append('end_date', endDate + 'T23:59:59Z');
        params.append('sort_by', sortBy);
        params.append('sort_order', sortOrder);
        
        filteredTournaments = await apiRequest(`/api/tournaments?${params.toString()}`);
        displayTournaments();
        updateTournamentSummary();
    } catch (error) {
        console.error('Failed to apply filters:', error);
    }
}

function clearTournamentFilters() {
    document.getElementById('tournament-start-date').value = '';
    document.getElementById('tournament-end-date').value = '';
    document.getElementById('tournament-sort').value = 'createdAt';
    document.getElementById('tournament-order').value = 'desc';
    
    filteredTournaments = [...tournaments];
    displayTournaments();
    updateTournamentSummary();
}

function displayTournaments() {
    const container = document.getElementById('tournaments-list');
    
    if (filteredTournaments.length === 0) {
        container.innerHTML = '<p>No tournaments found.</p>';
        return;
    }
    
    container.innerHTML = filteredTournaments.map(tournament => {
        const totalCommission = tournament.timeslots.reduce((sum, t) => sum + t.commission, 0);
        const totalPlayers = tournament.timeslots.reduce((sum, t) => sum + t.playersJoined, 0);
        const completedSlots = tournament.timeslots.filter(t => t.status === 'completed').length;
        
        return `
            <div class="tournament-card">
                <h3>${tournament.name}</h3>
                <p><strong>Type:</strong> ${tournament.type}</p>
                <p><strong>Created:</strong> ${new Date(tournament.createdAt).toLocaleDateString()}</p>
                <p><strong>Timeslots:</strong> ${tournament.timeslots.length} (${completedSlots} completed)</p>
                <p><strong>Total Players:</strong> ${totalPlayers}</p>
                <p><strong>Total Commission:</strong> $${totalCommission.toFixed(2)}</p>
                <div style="margin-top: 10px;">
                    <button class="btn btn-danger btn-small" onclick="deleteTournament('${tournament.id}')">Delete</button>
                </div>
            </div>
        `;
    }).join('');
}

function updateTournamentSummary() {
    const totalTournaments = tournaments.length;
    const totalCommission = tournaments.reduce((sum, t) => 
        sum + t.timeslots.reduce((tSum, ts) => tSum + ts.commission, 0), 0);
    const filteredCommission = filteredTournaments.reduce((sum, t) => 
        sum + t.timeslots.reduce((tSum, ts) => tSum + ts.commission, 0), 0);
    
    document.getElementById('total-tournaments').textContent = totalTournaments;
    document.getElementById('total-commission').textContent = `$${totalCommission.toFixed(2)}`;
    document.getElementById('filtered-commission').textContent = `$${filteredCommission.toFixed(2)}`;
}

async function deleteTournament(tournamentId) {
    if (!confirm('Are you sure you want to delete this tournament?')) return;

    try {
        await apiRequest(`/api/tournaments/${tournamentId}`, { method: 'DELETE' });
        showAlert('Tournament deleted successfully!', 'success');
        loadTournaments();
    } catch (error) {
        console.error('Failed to delete tournament:', error);
    }
}

// Tournament creation functions
async function handleTournamentSubmit(event) {
    event.preventDefault();

    const name = document.getElementById('tournament-name').value;
    const type = document.getElementById('tournament-type').value;

    if (!name || !type) {
        showAlert('Tournament name and type are required', 'error');
        return;
    }

    // Collect timeslot data
    const timeslots = [];
    const timeslotForms = document.querySelectorAll('.timeslot-form');

    for (const form of timeslotForms) {
        const date = form.querySelector('input[name="date"]').value;
        const playersJoined = form.querySelector('input[name="playersJoined"]').value;
        const commission = form.querySelector('input[name="commission"]').value;
        const status = form.querySelector('select[name="status"]').value;
        const notes = form.querySelector('textarea[name="notes"]').value;

        if (!date || !playersJoined || !commission || !status) {
            showAlert('All timeslot fields are required', 'error');
            return;
        }

        timeslots.push({
            date: date,
            playersJoined: parseInt(playersJoined),
            commission: parseFloat(commission),
            status: status,
            notes: notes
        });
    }

    try {
        const data = {
            name: name.trim(),
            type: type.trim(),
            timeslots: timeslots
        };

        await apiRequest('/api/tournaments', {
            method: 'POST',
            body: JSON.stringify(data)
        });

        showAlert('Tournament created successfully!', 'success');
        resetTournamentForm();
        loadTournaments();
    } catch (error) {
        console.error('Failed to create tournament:', error);
    }
}

function addTimeslot() {
    timeslotCounter++;
    const timeslotHtml = `
        <div class="timeslot-form" id="timeslot-${timeslotCounter}">
            <h4>Timeslot ${timeslotCounter}</h4>
            <div class="form-row">
                <div class="form-group">
                    <label>Date & Time:</label>
                    <input type="datetime-local" name="date" required />
                </div>
                <div class="form-group">
                    <label>Players Joined:</label>
                    <input type="number" name="playersJoined" min="0" required />
                </div>
                <div class="form-group">
                    <label>Commission:</label>
                    <input type="number" name="commission" min="0" step="0.01" required />
                </div>
                <div class="form-group">
                    <label>Status:</label>
                    <select name="status" required>
                        <option value="completed">Completed</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label>Notes (optional):</label>
                <textarea name="notes" rows="2" placeholder="Any additional notes..."></textarea>
            </div>
            <button type="button" class="btn btn-danger btn-small" onclick="removeTimeslot(${timeslotCounter})">Remove Timeslot</button>
        </div>
    `;
    document.getElementById('timeslots-list').insertAdjacentHTML('beforeend', timeslotHtml);
}

function removeTimeslot(id) {
    const timeslot = document.getElementById(`timeslot-${id}`);
    if (timeslot) {
        timeslot.remove();
    }
}

function resetTournamentForm() {
    document.getElementById('tournament-form').reset();
    document.getElementById('timeslots-list').innerHTML = '';
    timeslotCounter = 0;
    addTimeslot();
}

// FREE Games functions
async function loadFreeGames() {
    try {
        freeGames = await apiRequest('/api/free-games');
        filteredFreeGames = [...freeGames];
        displayFreeGames();
        updateFreeGamesSummary();
    } catch (error) {
        console.error('Failed to load free games:', error);
        document.getElementById('free-games-list').innerHTML = 
            '<div class="alert alert-error">Failed to load free games. Please try again.</div>';
    }
}

async function handleFreeGameSubmit(event) {
    event.preventDefault();
    
    const date = document.getElementById('freegame-date').value;
    const amount = document.getElementById('freegame-amount').value;
    const description = document.getElementById('freegame-description').value;
    
    if (!date || !amount) {
        showAlert('Date and amount are required', 'error');
        return;
    }
    
    try {
        const data = {
            date: date + 'T00:00:00Z',
            amount: parseFloat(amount),
            description: description.trim()
        };
        
        await apiRequest('/api/free-games', {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        showAlert('FREE Game record saved successfully!', 'success');
        
        // Reset form
        document.getElementById('freegame-amount').value = '';
        document.getElementById('freegame-description').value = '';
        
        // Reload data
        loadFreeGames();
    } catch (error) {
        console.error('Failed to save free game:', error);
    }
}

async function applyFreeGameFilters() {
    try {
        const startDate = document.getElementById('freegame-start-date').value;
        const endDate = document.getElementById('freegame-end-date').value;
        const sortBy = document.getElementById('freegame-sort').value;
        const sortOrder = document.getElementById('freegame-order').value;
        
        const params = new URLSearchParams();
        if (startDate) params.append('start_date', startDate + 'T00:00:00Z');
        if (endDate) params.append('end_date', endDate + 'T23:59:59Z');
        params.append('sort_by', sortBy);
        params.append('sort_order', sortOrder);
        
        filteredFreeGames = await apiRequest(`/api/free-games?${params.toString()}`);
        displayFreeGames();
        updateFreeGamesSummary();
    } catch (error) {
        console.error('Failed to apply filters:', error);
    }
}

function clearFreeGameFilters() {
    document.getElementById('freegame-start-date').value = '';
    document.getElementById('freegame-end-date').value = '';
    document.getElementById('freegame-sort').value = 'date';
    document.getElementById('freegame-order').value = 'desc';
    
    filteredFreeGames = [...freeGames];
    displayFreeGames();
    updateFreeGamesSummary();
}

function displayFreeGames() {
    const container = document.getElementById('free-games-list');
    
    if (filteredFreeGames.length === 0) {
        container.innerHTML = '<p>No free game records found.</p>';
        return;
    }
    
    container.innerHTML = filteredFreeGames.map(game => `
        <div class="free-game-card">
            <h3>Free Game Event</h3>
            <p><strong>Date:</strong> ${new Date(game.date).toLocaleDateString()}</p>
            <p><strong>Amount Spent:</strong> $${game.amount.toFixed(2)}</p>
            ${game.description ? `<p><strong>Description:</strong> ${game.description}</p>` : ''}
            <div style="margin-top: 10px;">
                <button class="btn btn-danger btn-small" onclick="deleteFreeGame('${game.id}')">Delete</button>
            </div>
        </div>
    `).join('');
}

function updateFreeGamesSummary() {
    const totalFreeGames = freeGames.length;
    const totalExpenditure = freeGames.reduce((sum, g) => sum + g.amount, 0);
    const filteredExpenditure = filteredFreeGames.reduce((sum, g) => sum + g.amount, 0);
    
    document.getElementById('total-free-games').textContent = totalFreeGames;
    document.getElementById('total-expenditure').textContent = `$${totalExpenditure.toFixed(2)}`;
    document.getElementById('filtered-expenditure').textContent = `$${filteredExpenditure.toFixed(2)}`;
}

async function deleteFreeGame(gameId) {
    if (!confirm('Are you sure you want to delete this free game record?')) return;
    
    try {
        await apiRequest(`/api/free-games/${gameId}`, { method: 'DELETE' });
        showAlert('Free game record deleted successfully!', 'success');
        loadFreeGames();
    } catch (error) {
        console.error('Failed to delete free game:', error);
    }
}

// Utility functions
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '1000';
    alertDiv.style.maxWidth = '400px';
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

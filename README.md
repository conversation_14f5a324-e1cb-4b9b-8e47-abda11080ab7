# Tournament Management Tool

A web-based tournament management application built with Flask and MongoDB, designed to track gaming tournaments and free game expenditures.

## Features

### Tournament Management
- Create and manage gaming tournaments
- Track multiple timeslots per tournament
- Record player participation and commission earnings
- Filter tournaments by date range
- Sort by date or total commission
- View comprehensive tournament statistics

### FREE Games Tracking
- Record expenditures for free game events
- Track dates, amounts, and descriptions
- Filter and sort free game records
- Monitor total expenditure across all events

### Security & Authentication
- Password-protected admin panel
- Session-based authentication
- Secure MongoDB integration

## Technology Stack

- **Backend**: Python Flask (compatible with Vercel serverless functions)
- **Database**: MongoDB Atlas
- **Frontend**: HTML5, CSS3, Vanilla JavaScript
- **Deployment**: Vercel

## Setup Instructions

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tournament-management-tool
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run the application**
   ```bash
   python api/index.py
   ```

5. **Access the application**
   - Open http://localhost:5000
   - Login with the admin password (default: admin123)

### Vercel Deployment

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel
   ```

3. **Set environment variables in Vercel dashboard**
   - `SECRET_KEY`: A secure random string for Flask sessions
   - `ADMIN_PASSWORD`: Your admin password

## Configuration

### Environment Variables

- `SECRET_KEY`: Flask secret key for sessions (required)
- `ADMIN_PASSWORD`: Password for admin access (default: admin123)
- `MONGODB_URI`: MongoDB connection string (already configured)

### MongoDB Setup

The application is pre-configured to use the provided MongoDB Atlas connection:
```
mongodb+srv://truckkun:<EMAIL>/?retryWrites=true&w=majority
```

Collections used:
- `tournaments`: Tournament data with timeslots
- `free_games`: Free game expenditure records

## API Endpoints

### Authentication
- `GET /`: Main application (requires authentication)
- `GET /login`: Login page
- `POST /login`: Authenticate user
- `GET /logout`: Logout user

### Tournaments
- `GET /api/tournaments`: Get all tournaments (with filtering/sorting)
- `POST /api/tournaments`: Create new tournament
- `PUT /api/tournaments/<id>`: Update tournament
- `DELETE /api/tournaments/<id>`: Delete tournament

### Free Games
- `GET /api/free-games`: Get all free game records (with filtering/sorting)
- `POST /api/free-games`: Create new free game record
- `PUT /api/free-games/<id>`: Update free game record
- `DELETE /api/free-games/<id>`: Delete free game record

## Data Models

### Tournament
```json
{
  "name": "Tournament Name",
  "type": "daily_paid",
  "timeslots": [
    {
      "date": "2024-01-01T10:00:00",
      "playersJoined": 5,
      "commission": 25.50,
      "status": "completed",
      "notes": "Optional notes"
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z"
}
```

### Free Game
```json
{
  "date": "2024-01-01T00:00:00Z",
  "amount": 50.00,
  "description": "Free game event description",
  "createdAt": "2024-01-01T00:00:00Z"
}
```

## Testing

Run the test script to validate functionality:

```bash
python test_app.py
```

Make sure the application is running before executing tests.

## Security Considerations

1. **Change default passwords**: Update `ADMIN_PASSWORD` in production
2. **Secure secret key**: Generate a strong `SECRET_KEY` for production
3. **HTTPS**: Always use HTTPS in production
4. **Database security**: The MongoDB connection uses authentication

## File Structure

```
├── api/
│   └── index.py          # Main Flask application
├── templates/
│   └── admin.html        # Admin panel template
├── static/
│   └── app.js           # Frontend JavaScript
├── requirements.txt      # Python dependencies
├── vercel.json          # Vercel configuration
├── test_app.py          # Test script
├── .env.example         # Environment variables template
└── README.md            # This file
```

## Support

For issues or questions, please check the application logs and ensure all dependencies are properly installed.

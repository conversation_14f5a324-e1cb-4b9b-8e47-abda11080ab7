# Environment variables for Tournament Management App

# Secret key for Flask sessions (generate a secure random key for production)
SECRET_KEY=your-secret-key-change-this-in-production

# Admin password for accessing the application
ADMIN_PASSWORD=admin123

# MongoDB connection string (already provided in the code)
# MONGODB_URI=mongodb+srv://truckkun:<EMAIL>/?retryWrites=true&w=majority

# Optional: Set to production for production deployment
# FLASK_ENV=production
